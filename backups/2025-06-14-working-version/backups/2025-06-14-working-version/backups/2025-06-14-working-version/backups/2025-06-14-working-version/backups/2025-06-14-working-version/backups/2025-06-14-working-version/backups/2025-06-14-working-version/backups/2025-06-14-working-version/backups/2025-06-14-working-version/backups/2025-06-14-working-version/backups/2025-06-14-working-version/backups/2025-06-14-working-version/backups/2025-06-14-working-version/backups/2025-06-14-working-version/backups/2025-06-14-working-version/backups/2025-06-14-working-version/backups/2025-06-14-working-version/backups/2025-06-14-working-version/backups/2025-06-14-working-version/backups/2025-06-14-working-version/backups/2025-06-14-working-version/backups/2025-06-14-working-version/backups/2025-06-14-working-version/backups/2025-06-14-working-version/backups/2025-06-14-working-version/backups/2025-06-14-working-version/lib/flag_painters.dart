import 'package:flutter/material.dart';
import 'dart:math' as math;

// Nepal Flag Painter
class NepalFlagPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint redPaint = Paint()
      ..color = const Color(0xFFC8102E)
      ..style = PaintingStyle.fill;

    final Paint bluePaint = Paint()
      ..color = const Color(0xFF003893)
      ..style = PaintingStyle.fill;

    final Paint whitePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Draw the crimson background
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), redPaint);

    // Draw the blue border
    final Path borderPath = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width * 0.7, size.height * 0.5)
      ..lineTo(0, size.height)
      ..lineTo(0, 0);

    canvas.drawPath(borderPath, bluePaint);

    // Draw the moon
    final moonCenter = Offset(size.width * 0.25, size.height * 0.25);
    canvas.drawCircle(moonCenter, size.height * 0.15, whitePaint);
    
    // Draw the inner circle to create crescent
    final innerMoonCenter = Offset(size.width * 0.28, size.height * 0.25);
    canvas.drawCircle(innerMoonCenter, size.height * 0.12, redPaint);

    // Draw the sun
    final sunCenter = Offset(size.width * 0.25, size.height * 0.75);
    canvas.drawCircle(sunCenter, size.height * 0.12, whitePaint);
    
    // Draw sun rays
    final sunRayPath = Path();
    for (int i = 0; i < 12; i++) {
      final angle = i * (math.pi / 6);
      final x1 = sunCenter.dx + math.cos(angle) * size.height * 0.12;
      final y1 = sunCenter.dy + math.sin(angle) * size.height * 0.12;
      final x2 = sunCenter.dx + math.cos(angle) * size.height * 0.18;
      final y2 = sunCenter.dy + math.sin(angle) * size.height * 0.18;
      
      sunRayPath.moveTo(x1, y1);
      sunRayPath.lineTo(x2, y2);
    }
    
    canvas.drawPath(
      sunRayPath,
      Paint()
        ..color = whitePaint.color
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// UK Flag Painter
class UKFlagPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint redPaint = Paint()
      ..color = const Color(0xFFC8102E)
      ..style = PaintingStyle.fill;

    final Paint whitePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final Paint bluePaint = Paint()
      ..color = const Color(0xFF012169)
      ..style = PaintingStyle.fill;

    // Draw blue background
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bluePaint);

    // Draw white diagonal cross (St. Patrick's cross)
    final Path whiteXPath = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width * 0.1, 0)
      ..lineTo(size.width, size.height * 0.9)
      ..lineTo(size.width, size.height)
      ..lineTo(size.width * 0.9, size.height)
      ..lineTo(0, size.height * 0.1)
      ..close();

    final Path whiteXPath2 = Path()
      ..moveTo(size.width, 0)
      ..lineTo(size.width, size.height * 0.1)
      ..lineTo(size.width * 0.1, size.height)
      ..lineTo(0, size.height)
      ..lineTo(0, size.height * 0.9)
      ..lineTo(size.width * 0.9, 0)
      ..close();

    canvas.drawPath(whiteXPath, whitePaint);
    canvas.drawPath(whiteXPath2, whitePaint);

    // Draw red diagonal cross (St. Patrick's cross)
    final Path redXPath = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width * 0.05, 0)
      ..lineTo(size.width, size.height * 0.95)
      ..lineTo(size.width, size.height)
      ..lineTo(size.width * 0.95, size.height)
      ..lineTo(0, size.height * 0.05)
      ..close();

    final Path redXPath2 = Path()
      ..moveTo(size.width, 0)
      ..lineTo(size.width, size.height * 0.05)
      ..lineTo(size.width * 0.05, size.height)
      ..lineTo(0, size.height)
      ..lineTo(0, size.height * 0.95)
      ..lineTo(size.width * 0.95, 0)
      ..close();

    canvas.drawPath(redXPath, redPaint);
    canvas.drawPath(redXPath2, redPaint);

    // Draw white cross (St. George's cross)
    final whiteHorizontal = Rect.fromLTWH(
      0,
      size.height * 0.4,
      size.width,
      size.height * 0.2,
    );
    canvas.drawRect(whiteHorizontal, whitePaint);

    final whiteVertical = Rect.fromLTWH(
      size.width * 0.4,
      0,
      size.width * 0.2,
      size.height,
    );
    canvas.drawRect(whiteVertical, whitePaint);

    // Draw red cross (St. George's cross)
    final redHorizontal = Rect.fromLTWH(
      0,
      size.height * 0.45,
      size.width,
      size.height * 0.1,
    );
    canvas.drawRect(redHorizontal, redPaint);

    final redVertical = Rect.fromLTWH(
      size.width * 0.45,
      0,
      size.width * 0.1,
      size.height,
    );
    canvas.drawRect(redVertical, redPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
