-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:2:5-38:19
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
MERGED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae03ddb689964a197cc9572a48c09923/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae03ddb689964a197cc9572a48c09923/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:1:1-50:12
MERGED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:1:1-50:12
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:2:1-13:12
MERGED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-35:12
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5683f3a75f4aae74864d56d25d09ab49/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aea3dee6044b352a54f8fb9c4a12c07/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d13c603c9df183737e377f596dd9c514/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2df645d79e711076ea45b6a8385e99c/transformed/recyclerview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb5bbbc1627b43bb066b846f50b0b9c/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3ef3caec9d424994a2628e26767d1d4/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2a5284c6eeabc2d8b6190c9576956065/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/32d7d149835c8c00f0697dfcab9adb84/transformed/appcompat-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dd8f2b9cf49e7284b6695565297622e3/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/812b0debb255957f3fcc9a2669f27c22/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dc9ec47ea914a5029eb4af212fbfb973/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7849ccecc13e92bd92ae7e14119101a3/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c4a6ad3f7b10ac103c0346368799501/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf593104563b66144c3b6dc7fc4802c1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ff6adcf87eb4959e12e2c03b49ab87a/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c8820905eb370fbd3bb42d26ba8ae5a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/645c56368631721d8b7755f7e8a726a8/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8c201c583c6fe469398993e61fede1c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/594b8e088512a24631e5a37668efcdba/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/07de910142823f029a6feac220e8ca4a/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7f3b092f04c0253e979c83421f120f88/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f57bd372fc0b34c3bf33092f8edd1/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3406eb4cffe3e53bb6d20996e0b4ec87/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/834fbfef458f2f76792d20bc77011687/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/538273fdc918a79c3b54859282434b2c/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e71467fe7caeb25f99b2fabbeba6edee/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1ff8cdab1140e9ed76ee2cc1c84e5b27/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9108df622b0b8e8dbba78b566c3bdc67/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6435e177046c519b4e9a09d53555b3f/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/348e9995eb75a687bf31011ee5dfb249/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3e9c3e49153aa47f3a49791ab647f926/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0fa1677c034ef049b8c12b4202dcc8c4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2111a692760d2213c419f21507346c83/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbe59546d7834ae0953d7d1775d43fac/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7295a6e9f96a0daeed321ae2faac1f7b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67e6cf8dd6496a1e1ab42bfbc69e3371/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae03ddb689964a197cc9572a48c09923/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7620a15c947fbbae2a35989fac4ced29/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/decb1acca50446f7d04f9c3baaa30a43/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d4ce3ca3f2edcf65c5795f97027f83c4/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b71b52b2a70c6b10140d87dce8249e38/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/24b2d70c983456e4678be6d7afa07780/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdf4cc3b708776cdc2805e90540cbeb3/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a192cd1e8c493370fa6465d68c1145/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/38895ff92e9ea1cab9c97b52c490e983/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/14a7b710af8666cefae7796bd89d044c/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/adc76dc2b16c544bb53f946048eea1be/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71bc0c4f568ebbce86450b445dadf8a3/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a9ec07ac357bc15a8d85f12e6d4189c/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:1:11-69
queries
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:44:5-49:15
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:7:5-11:15
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:7:5-11:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:45:9-48:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:46:13-72
	android:name
		ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:46:21-70
data
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:47:13-50
	android:mimeType
		ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/main/AndroidManifest.xml:47:19-48
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml:6:5-66
	android:name
		ADDED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5683f3a75f4aae74864d56d25d09ab49/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5683f3a75f4aae74864d56d25d09ab49/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aea3dee6044b352a54f8fb9c4a12c07/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aea3dee6044b352a54f8fb9c4a12c07/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d13c603c9df183737e377f596dd9c514/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d13c603c9df183737e377f596dd9c514/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2df645d79e711076ea45b6a8385e99c/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2df645d79e711076ea45b6a8385e99c/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb5bbbc1627b43bb066b846f50b0b9c/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb5bbbc1627b43bb066b846f50b0b9c/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3ef3caec9d424994a2628e26767d1d4/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3ef3caec9d424994a2628e26767d1d4/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2a5284c6eeabc2d8b6190c9576956065/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2a5284c6eeabc2d8b6190c9576956065/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/32d7d149835c8c00f0697dfcab9adb84/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/32d7d149835c8c00f0697dfcab9adb84/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dd8f2b9cf49e7284b6695565297622e3/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dd8f2b9cf49e7284b6695565297622e3/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/812b0debb255957f3fcc9a2669f27c22/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/812b0debb255957f3fcc9a2669f27c22/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dc9ec47ea914a5029eb4af212fbfb973/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dc9ec47ea914a5029eb4af212fbfb973/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7849ccecc13e92bd92ae7e14119101a3/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7849ccecc13e92bd92ae7e14119101a3/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c4a6ad3f7b10ac103c0346368799501/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c4a6ad3f7b10ac103c0346368799501/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf593104563b66144c3b6dc7fc4802c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf593104563b66144c3b6dc7fc4802c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ff6adcf87eb4959e12e2c03b49ab87a/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ff6adcf87eb4959e12e2c03b49ab87a/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c8820905eb370fbd3bb42d26ba8ae5a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c8820905eb370fbd3bb42d26ba8ae5a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/645c56368631721d8b7755f7e8a726a8/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/645c56368631721d8b7755f7e8a726a8/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8c201c583c6fe469398993e61fede1c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8c201c583c6fe469398993e61fede1c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/594b8e088512a24631e5a37668efcdba/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/594b8e088512a24631e5a37668efcdba/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/07de910142823f029a6feac220e8ca4a/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/07de910142823f029a6feac220e8ca4a/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7f3b092f04c0253e979c83421f120f88/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7f3b092f04c0253e979c83421f120f88/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f57bd372fc0b34c3bf33092f8edd1/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f57bd372fc0b34c3bf33092f8edd1/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3406eb4cffe3e53bb6d20996e0b4ec87/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3406eb4cffe3e53bb6d20996e0b4ec87/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/834fbfef458f2f76792d20bc77011687/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/834fbfef458f2f76792d20bc77011687/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/538273fdc918a79c3b54859282434b2c/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/538273fdc918a79c3b54859282434b2c/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e71467fe7caeb25f99b2fabbeba6edee/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e71467fe7caeb25f99b2fabbeba6edee/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1ff8cdab1140e9ed76ee2cc1c84e5b27/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1ff8cdab1140e9ed76ee2cc1c84e5b27/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9108df622b0b8e8dbba78b566c3bdc67/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9108df622b0b8e8dbba78b566c3bdc67/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6435e177046c519b4e9a09d53555b3f/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6435e177046c519b4e9a09d53555b3f/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/348e9995eb75a687bf31011ee5dfb249/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/348e9995eb75a687bf31011ee5dfb249/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3e9c3e49153aa47f3a49791ab647f926/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3e9c3e49153aa47f3a49791ab647f926/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0fa1677c034ef049b8c12b4202dcc8c4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0fa1677c034ef049b8c12b4202dcc8c4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2111a692760d2213c419f21507346c83/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2111a692760d2213c419f21507346c83/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbe59546d7834ae0953d7d1775d43fac/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbe59546d7834ae0953d7d1775d43fac/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7295a6e9f96a0daeed321ae2faac1f7b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7295a6e9f96a0daeed321ae2faac1f7b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67e6cf8dd6496a1e1ab42bfbc69e3371/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67e6cf8dd6496a1e1ab42bfbc69e3371/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae03ddb689964a197cc9572a48c09923/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae03ddb689964a197cc9572a48c09923/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7620a15c947fbbae2a35989fac4ced29/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7620a15c947fbbae2a35989fac4ced29/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/decb1acca50446f7d04f9c3baaa30a43/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/decb1acca50446f7d04f9c3baaa30a43/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d4ce3ca3f2edcf65c5795f97027f83c4/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d4ce3ca3f2edcf65c5795f97027f83c4/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b71b52b2a70c6b10140d87dce8249e38/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b71b52b2a70c6b10140d87dce8249e38/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/24b2d70c983456e4678be6d7afa07780/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/24b2d70c983456e4678be6d7afa07780/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdf4cc3b708776cdc2805e90540cbeb3/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdf4cc3b708776cdc2805e90540cbeb3/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a192cd1e8c493370fa6465d68c1145/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a192cd1e8c493370fa6465d68c1145/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/38895ff92e9ea1cab9c97b52c490e983/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/38895ff92e9ea1cab9c97b52c490e983/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/14a7b710af8666cefae7796bd89d044c/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/14a7b710af8666cefae7796bd89d044c/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/adc76dc2b16c544bb53f946048eea1be/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/adc76dc2b16c544bb53f946048eea1be/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71bc0c4f568ebbce86450b445dadf8a3/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71bc0c4f568ebbce86450b445dadf8a3/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a9ec07ac357bc15a8d85f12e6d4189c/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a9ec07ac357bc15a8d85f12e6d4189c/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/android/app/src/debug/AndroidManifest.xml
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:13-90
	android:name
		ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2003a59c2c246cb9d6483bbd63bc7aff/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:21-87
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/nepali_results_backup_complete_20250612_022514/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/970764a81dd610c772147726baa7dca5/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/77d6589d54f70503af1ad7bd5c95eb07/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e920d985392df03e5b02e06877a52d21/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.nepali_results_working.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.nepali_results_working.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/deb838f5c4813d0c441214e1ddf497cc/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/eabc1325411a824ae74b9b450245750e/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
