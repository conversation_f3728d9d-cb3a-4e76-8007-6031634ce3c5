import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class AgeCalculator extends StatefulWidget {
  const AgeCalculator({super.key});

  @override
  State<AgeCalculator> createState() => _AgeCalculatorState();
}

class _AgeCalculatorState extends State<AgeCalculator> {
  final TextEditingController _dobController = TextEditingController();
  DateTime? _selectedDate;
  Map<String, dynamic>? _ageDetails;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _dobController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (context, child) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: isDarkMode
                ? ColorScheme.dark(
                    primary: Colors.blue.shade700,
                    onPrimary: Colors.white,
                    onSurface: Colors.white,
                    surface: Colors.grey[850]!,
                  )
                : ColorScheme.light(
                    primary: Colors.blue.shade700,
                    onPrimary: Colors.white,
                    onSurface: Colors.black,
                  ),
            dialogBackgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dobController.text = "${picked.day}/${picked.month}/${picked.year}";
        _calculateAge();
      });
    }
  }

  void _calculateAge() {
    if (_selectedDate == null) return;

    final now = DateTime.now();
    final dob = _selectedDate!;

    // Basic validation
    if (dob.isAfter(now)) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    // Calculate years, months, and days
    int years = now.year - dob.year;
    int months = now.month - dob.month;
    int days = now.day - dob.day;

    // Adjust for negative months or days
    if (days < 0) {
      // Go back one month
      months--;
      // Add days in the previous month
      days += _daysInMonth(now.year, now.month - 1);
    }

    if (months < 0) {
      // Go back one year
      years--;
      // Add 12 months
      months += 12;
    }

    // Calculate total values
    final totalDays = now.difference(dob).inDays;
    final totalMonths = (years * 12) + months;
    final totalWeeks = totalDays ~/ 7;
    final totalHours = totalDays * 24;
    final totalMinutes = totalHours * 60;

    setState(() {
      _ageDetails = {
        'years': years,
        'months': months,
        'days': days,
        'totalMonths': totalMonths,
        'totalWeeks': totalWeeks,
        'totalDays': totalDays,
        'totalHours': totalHours,
        'totalMinutes': totalMinutes,
      };
    });
  }

  int _daysInMonth(int year, int month) {
    // Adjust month if it's out of range
    if (month <= 0) {
      year--;
      month += 12;
    }
    
    // Handle leap years for February
    if (month == 2) {
      return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
    }
    
    // Return days for other months
    const daysInMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    return daysInMonth[month];
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Age Calculator',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Input Section
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enter Date of Birth',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _dobController,
                      readOnly: true,
                      onTap: () => _selectDate(context),
                      decoration: InputDecoration(
                        labelText: 'Date of Birth',
                        hintText: 'DD/MM/YYYY',
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.calendar_today),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            setState(() {
                              _dobController.clear();
                              _selectedDate = null;
                              _ageDetails = null;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _selectedDate != null ? _calculateAge : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade700,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Calculate Age',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Results Section
            if (_ageDetails != null)
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.cake, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Age Details',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildResultItem(
                        'Years, Months & Days',
                        '${_ageDetails!['years']} years, ${_ageDetails!['months']} months, ${_ageDetails!['days']} days',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Months',
                        '${_ageDetails!['totalMonths']} months',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Weeks',
                        '${_ageDetails!['totalWeeks']} weeks',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Days',
                        '${_ageDetails!['totalDays']} days',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Hours',
                        '${_ageDetails!['totalHours']} hours',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Minutes',
                        '${_ageDetails!['totalMinutes']} minutes',
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Info Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Information',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• Tap on the date field to select your date of birth\n'
                      '• Age is calculated based on the current date and time\n'
                      '• Results show your age in various time units',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}