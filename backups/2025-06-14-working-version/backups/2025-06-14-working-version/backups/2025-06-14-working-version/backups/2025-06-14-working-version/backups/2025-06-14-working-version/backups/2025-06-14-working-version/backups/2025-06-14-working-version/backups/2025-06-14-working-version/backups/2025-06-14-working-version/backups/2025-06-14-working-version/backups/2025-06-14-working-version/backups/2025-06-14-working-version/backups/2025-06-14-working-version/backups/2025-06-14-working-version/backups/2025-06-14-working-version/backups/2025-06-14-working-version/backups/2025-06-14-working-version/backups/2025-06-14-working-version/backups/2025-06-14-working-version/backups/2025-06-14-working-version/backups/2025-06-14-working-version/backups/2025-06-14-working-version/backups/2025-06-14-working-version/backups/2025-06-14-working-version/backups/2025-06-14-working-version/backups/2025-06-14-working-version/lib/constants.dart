import 'package:flutter/material.dart';
import 'models/result_category.dart';

class AppConstants {
  // App name
  static const String appName = 'Nepali Results';

  // Colors
  static const Color primaryColor = Color(0xFF1976D2); // Blue
  static const Color secondaryColor = Color(0xFF4CAF50); // Green
  static const Color accentColor = Color(0xFFFFFFFF); // White

  // Category-specific colors
  static const Map<String, Color> categoryColors = {
    'SEE': Color(0xFF1976D2), // Blue
    'HSEB': Color(0xFF3F51B5), // Indigo
    'TU': Color(0xFF673AB7), // Deep Purple
    'KU': Color(0xFF009688), // Teal
    'PU': Color(0xFF8BC34A), // Light Green
    'CTEVT': Color(0xFFFF9800), // Orange
    'DV': Color(0xFF4CAF50), // Green
    'IPO': Color(0xFFE91E63), // Pink
  };

  // URLs for exam results
  static const Map<String, Map<String, String>> resultUrls = {
    'SEE': {
      'Official Site': 'http://see.gov.np/exam/results',
      'Check via NTC': 'https://see.ntc.net.np/',
      'View on eKantipur':
          'https://results.ekantipur.com/see-results-with-marksheet.php',
    },
    'HSEB': {
      'Official Website': 'https://www.neb.gov.np/results',
      'NTC Portal': 'https://neb.ntc.net.np/',
    },
    'TU': {'Official Website': 'https://result.tuexam.edu.np/'},
    'KU': {'Official Website': 'https://examresults.ku.edu.np:81/'},
    'PU': {'Official Website': 'https://puexam.edu.np/find-results_new'},
    'DV': {'Reliable Website': 'https://dvprogram.state.gov/ESC/Default.aspx'},
    'CTEVT': {
      'Official Website': 'https://itms.ctevt.org.np:5580/check_results',
    },
    'IPO': {
      'CDSC IPO Result': 'https://iporesult.cdsc.com.np/',
    },
  };

  // SMS information for exams that support SMS results
  static final Map<String, SmsInfo> smsInfo = {
    'SEE': SmsInfo(
      format: 'SEE <SymbolNumber>',
      sendTo: '1600',
      example: 'SEE 1234567D',
      alternateFormat: 'SEE <SymbolNumber>',
      alternateSendTo: '35001',
      ivrNumber: '1600',
    ),
    'TU': SmsInfo(
      format: '4 <SymbolNumber>',
      sendTo: '33624',
      example: '4 123456',
    ),

    'HSEB': SmsInfo(
      format: 'NEB <SymbolNumber>',
      sendTo: '1600',
      example: 'NEB 1234567',
      alternateFormat: 'NEB <SymbolNumber>',
      alternateSendTo: '35001',
      ivrNumber: '1601',
    ),
  };

  // Icons for each category
  static const Map<String, IconData> categoryIcons = {
    'SEE': Icons.school,
    'HSEB': Icons.article_outlined,
    'TU': Icons.account_balance,
    'KU': Icons.school_outlined,
    'PU': Icons.account_balance_outlined,
    'DV': Icons.public,
    'CTEVT': Icons.school_outlined,
    'IPO': Icons.trending_up,
  };

  // Descriptions for each category
  static const Map<String, String> categoryDescriptions = {
    'SEE': 'Secondary Education Examination Results',
    'HSEB': '',
    'TU': 'Tribhuvan University Results',
    'KU': 'Kathmandu University Results',
    'PU': 'Purbanchal University Results',
    'DV': 'Diversity Visa Lottery Results',
    'CTEVT': 'Council for Technical Education and Vocational Training Results',
    'IPO': 'Initial Public Offering Results',
  };

  // Tags for each category
  static const Map<String, String> categoryTags = {
    'SEE': 'Official',
    'HSEB': 'Official',
    'TU': 'Popular',
    'KU': 'University',
    'PU': 'University',
    'DV': 'International',
    'CTEVT': 'Technical',
    'IPO': 'Financial',
  };
}
