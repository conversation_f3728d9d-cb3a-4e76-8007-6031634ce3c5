import 'package:flutter/material.dart';

class SmsInfo {
  final String format;
  final String sendTo;
  final String example;
  final String? alternateFormat;
  final String? alternateSendTo;
  final String? ivrNumber;

  const SmsInfo({
    required this.format,
    required this.sendTo,
    required this.example,
    this.alternateFormat,
    this.alternateSendTo,
    this.ivrNumber,
  });
}

class ResultCategory {
  final String name;
  final String description;
  final IconData icon;
  final Map<String, String> resultUrls;
  final SmsInfo? smsInfo;

  ResultCategory({
    required this.name,
    required this.description,
    required this.icon,
    required this.resultUrls,
    this.smsInfo,
  });

  bool get hasSmsSupport => smsInfo != null;
}
