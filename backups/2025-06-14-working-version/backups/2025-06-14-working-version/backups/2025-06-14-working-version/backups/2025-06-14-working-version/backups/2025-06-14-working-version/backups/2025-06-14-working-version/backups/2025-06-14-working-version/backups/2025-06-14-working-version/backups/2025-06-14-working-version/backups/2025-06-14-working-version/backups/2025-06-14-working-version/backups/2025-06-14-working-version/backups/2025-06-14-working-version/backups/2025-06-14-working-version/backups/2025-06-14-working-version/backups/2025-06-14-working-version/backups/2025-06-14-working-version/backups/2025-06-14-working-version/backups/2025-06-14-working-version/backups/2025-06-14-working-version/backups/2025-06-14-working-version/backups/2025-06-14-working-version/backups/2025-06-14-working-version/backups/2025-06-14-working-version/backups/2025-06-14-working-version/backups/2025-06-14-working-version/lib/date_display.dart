import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DateDisplay extends StatefulWidget {
  final bool isCompact;

  const DateDisplay({super.key, this.isCompact = false});

  @override
  State<DateDisplay> createState() => _DateDisplayState();
}

class _DateDisplayState extends State<DateDisplay> {
  late NepaliDateTime _currentNepaliDate;
  String? _cachedDateText;

  @override
  void initState() {
    super.initState();
    _loadCachedDate();
    _updateDate();
    // Update date every minute to keep it current
    _startDateTimer();
  }

  Future<void> _loadCachedDate() async {
    final prefs = await SharedPreferences.getInstance();
    final cachedDate = prefs.getString('nepali_date_display');
    final lastUpdateDate = prefs.getString('nepali_date_last_update');

    // Check if cached date is from today
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    if (cachedDate != null && lastUpdateDate == todayString) {
      setState(() {
        _cachedDateText = cachedDate;
      });
    }
  }

  Future<void> _saveDateToCache(String dateText) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    await prefs.setString('nepali_date_display', dateText);
    await prefs.setString('nepali_date_last_update', todayString);
  }

  void _updateDate() {
    setState(() {
      _currentNepaliDate = NepaliDateTime.now();
    });

    // Save to cache when date updates
    final dateText = _formatNepaliDate();
    _saveDateToCache(dateText);
  }

  void _startDateTimer() {
    // Update every minute
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _updateDate();
        _startDateTimer();
      }
    });
  }

  String _formatNepaliDate() {
    // Format the Nepali date properly
    final nepaliYear = _currentNepaliDate.year;
    final nepaliDay = _currentNepaliDate.day;
    
    // Get month and weekday in Nepali
    String nepaliMonth = '';
    switch (_currentNepaliDate.month) {
      case 1: nepaliMonth = 'बैशाख'; break;
      case 2: nepaliMonth = 'जेठ'; break;
      case 3: nepaliMonth = 'असार'; break;
      case 4: nepaliMonth = 'श्रावण'; break;
      case 5: nepaliMonth = 'भदौ'; break;
      case 6: nepaliMonth = 'असोज'; break;
      case 7: nepaliMonth = 'कार्तिक'; break;
      case 8: nepaliMonth = 'मंसिर'; break;
      case 9: nepaliMonth = 'पुष'; break;
      case 10: nepaliMonth = 'माघ'; break;
      case 11: nepaliMonth = 'फागुन'; break;
      case 12: nepaliMonth = 'चैत'; break;
    }
    
    // Get the weekday directly from NepaliDateFormat
    String englishWeekday = NepaliDateFormat.EEEE().format(_currentNepaliDate);
    
    // Map English weekday to Nepali Unicode weekday
    String nepaliWeekday = '';
    switch (englishWeekday.toLowerCase()) {
      case 'sunday': nepaliWeekday = 'आइतबार'; break;
      case 'monday': nepaliWeekday = 'सोमबार'; break;
      case 'tuesday': nepaliWeekday = 'मंगलबार'; break;
      case 'wednesday': nepaliWeekday = 'बुधबार'; break;
      case 'thursday': nepaliWeekday = 'बिहिबार'; break;
      case 'friday': nepaliWeekday = 'शुक्रबार'; break;
      case 'saturday': nepaliWeekday = 'शनिबार'; break;
      default: nepaliWeekday = 'शुक्रबार'; // Default to Friday if something goes wrong
    }

    // Convert numbers to Nepali numerals
    final nepaliNumerals = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    String convertToNepaliNumerals(int number) {
      return number.toString().split('').map((digit) {
        return nepaliNumerals[int.parse(digit)];
      }).join('');
    }

    final nepaliYearStr = convertToNepaliNumerals(nepaliYear);
    final nepaliDayStr = convertToNepaliNumerals(nepaliDay);

    // Format: "📅 २०८२ जेठ २९ शुक्रबार" - fully in Nepali Unicode with calendar emoji
    return '📅 $nepaliYearStr $nepaliMonth $nepaliDayStr $nepaliWeekday';
  }

  @override
  Widget build(BuildContext context) {
    final dateText = _cachedDateText ?? _formatNepaliDate();

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end, // Align to the right
      children: [
        // Calendar emoji is now part of the formatted text string
        Flexible(
          child: Text(
            dateText,
            style: GoogleFonts.poppins(
              fontSize: widget.isCompact ? 14 : 16,
              fontWeight: widget.isCompact ? FontWeight.w500 : FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.right, // Right-align the text
          ),
        ),
      ],
    );
  }
}