import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'result_category.dart';
import 'constants.dart';
import 'theme_provider.dart';
import 'utils/notification_provider.dart';
import 'grid_category_card.dart';
import 'date_display.dart';
import 'result_page.dart';
import 'about_page.dart';


import 'notification_screen.dart';
import 'services/chrome_custom_tabs_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late List<ResultCategory> _categories;

  @override
  void initState() {
    super.initState();
    _initializeCategories();
  }

  void _initializeCategories() {
    // Create categories from constants
    _categories =
        AppConstants.resultUrls.entries.map((entry) {
          final categoryName = entry.key;
          return ResultCategory(
            name: categoryName,
            description: AppConstants.categoryDescriptions[categoryName] ?? '',
            icon:
                AppConstants.categoryIcons[categoryName] ?? Icons.help_outline,
            resultUrls: entry.value,
            smsInfo: AppConstants.smsInfo[categoryName],
          );
        }).toList();

    // Sort categories to ensure specific order
    _categories.sort((a, b) {
      // Define the order of categories - IPO moved to second-last position
      const order = ['SEE', 'HSEB', 'TU', 'KU', 'PU', 'CTEVT', 'IPO', 'DV'];
      return order.indexOf(a.name).compareTo(order.indexOf(b.name));
    });
    
    // Note: We can't modify the description directly as it's final
    // The IPO description should be updated in the constants.dart file where categories are defined
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // Remove AppBar completely to avoid interfering with status bar
        appBar: null,
      body: Column(
        children: [
          // Header section with proper SafeArea to avoid status bar overlap
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.red.shade700,
                  Colors.red.shade400,
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // Top row with Nepali date and action icons - with 2.5x more spacing from status bar
                  Container(
                    padding: const EdgeInsets.fromLTRB(16.0, 40.0, 16.0, 8.0),
                    child: Row(
                      children: [
                        // Nepali date (calendar icon already included in DateDisplay)
                        Expanded(
                          child: DateDisplay(isCompact: true),
                        ),
                        // Action icons on the right
                        Row(
                          children: [
                            // Notification Icon with Badge
                            Consumer<NotificationProvider>(
                              builder: (context, notificationProvider, _) {
                                return Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.notifications_outlined),
                                      color: Colors.white,
                                      tooltip: 'Notifications',
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const NotificationScreen(),
                                          ),
                                        );
                                      },
                                      splashRadius: 24,
                                    ),
                                    // Only show badge if notifications are enabled and count > 0
                                    if (notificationProvider.areNotificationsEnabled &&
                                        notificationProvider.notificationCount > 0)
                                      Positioned(
                                        top: 8,
                                        right: 8,
                                        child: Container(
                                          padding: const EdgeInsets.all(2),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          constraints: const BoxConstraints(
                                            minWidth: 16,
                                            minHeight: 16,
                                          ),
                                          child: Center(
                                            child: Text(
                                              notificationProvider.notificationCount > 9
                                                  ? '9+'
                                                  : notificationProvider.notificationCount
                                                      .toString(),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                );
                              },
                            ),
                            // Dark Mode Toggle (moon icon)
                            Consumer<ThemeProvider>(
                              builder: (context, themeProvider, _) {
                                return IconButton(
                                  icon: Icon(
                                    themeProvider.isDarkMode
                                        ? Icons.wb_sunny
                                        : Icons.nightlight_round,
                                    color: Colors.white,
                                  ),
                                  tooltip: themeProvider.isDarkMode ? 'Light Mode' : 'Dark Mode',
                                  onPressed: () {
                                    themeProvider.toggleTheme();
                                  },
                                  splashRadius: 24,
                                );
                              },
                            ),
                            // More Options Menu
                            PopupMenuButton<String>(
                              icon: const Icon(
                                Icons.more_vert,
                                color: Colors.white,
                              ),
                              tooltip: 'More Options',
                              position: PopupMenuPosition.under,
                              offset: const Offset(0, 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              onSelected: (value) async {
                                switch (value) {
                                  case 'about':
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (context) => const AboutPage()),
                                    );
                                    break;
                                  case 'terms':
                                    try {
                                      await ChromeCustomTabsService.launch(
                                        'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-body-font-family.html',
                                        title: 'Terms and Conditions',
                                      );
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('Could not open Terms and Conditions: ${e.toString()}')),
                                        );
                                      }
                                    }
                                    break;
                                  case 'privacy':
                                    try {
                                      await ChromeCustomTabsService.launch(
                                        'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-privacy-policy.html',
                                        title: 'Privacy Policy',
                                      );
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('Could not open Privacy Policy: ${e.toString()}')),
                                        );
                                      }
                                    }
                                    break;
                                }
                              },
                              itemBuilder: (BuildContext context) {
                                return [
                                  PopupMenuItem<String>(
                                    value: 'about',
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.info_outline,
                                          size: 20,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'About App',
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w500,
                                            color: Theme.of(context).textTheme.bodyLarge?.color,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<String>(
                                    value: 'terms',
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.description_outlined,
                                          size: 20,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Terms & Conditions',
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w500,
                                            color: Theme.of(context).textTheme.bodyLarge?.color,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem<String>(
                                    value: 'privacy',
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.privacy_tip_outlined,
                                          size: 20,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Privacy Policy',
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w500,
                                            color: Theme.of(context).textTheme.bodyLarge?.color,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ];
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // App title and subtitle section
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
                    child: Column(
                      children: [
                        Text(
                          AppConstants.appName,
                          style: GoogleFonts.poppins(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "Check your results easily",
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

            // Grid of result categories
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: GridView.builder(
                  physics: const BouncingScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount:
                        MediaQuery.of(context).size.width > 600 ? 3 : 2,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    return GridCategoryCard(
                      category: category,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ResultPage(category: category),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom navigation bar is now handled by MainLayout
    );
  }
}
