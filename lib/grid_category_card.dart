import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'result_category.dart';
import 'constants.dart';

class GridCategoryCard extends StatelessWidget {
  final ResultCategory category;
  final VoidCallback onTap;

  const GridCategoryCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Get category-specific color or use primary color as fallback
    final categoryColor =
        AppConstants.categoryColors[category.name] ??
        Theme.of(context).colorScheme.primary;

    // Adjust colors based on theme
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: isDarkMode ? 8 : 4,
      shadowColor:
          isDarkMode
              ? categoryColor.withAlpha(100)
              : categoryColor.withAlpha(60),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side:
            isDarkMode
                ? BorderSide(color: categoryColor.with<PERSON><PERSON><PERSON>(80), width: 1.5)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        splashColor: categoryColor.withAlpha(50),
        highlightColor: categoryColor.withAlpha(30),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient:
                isDarkMode
                    ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF1E1E2D).withAlpha(255),
                        const Color(0xFF1A1A2E).withAlpha(255),
                      ],
                    )
                    : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        categoryColor.withAlpha(30),
                        categoryColor.withAlpha(10),
                      ],
                    ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Custom image for each category
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        isDarkMode
                            ? const Color(0xFF2A2A40) // Dark blue-purple
                            : Colors.white,
                    shape: BoxShape.circle,
                    border:
                        isDarkMode
                            ? Border.all(
                              color: categoryColor.withAlpha(150),
                              width: 2.0,
                            )
                            : null,
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDarkMode
                                ? categoryColor.withAlpha(100)
                                : categoryColor.withAlpha(60),
                        blurRadius: isDarkMode ? 12 : 8,
                        spreadRadius: isDarkMode ? 1 : 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child:
                      category.name == 'SEE'
                          ? Image.asset(
                            'assets/images/see_results2.png',
                            width: 50,
                            height: 50,
                          )
                          : category.name == 'HSEB'
                          ? Image.asset(
                            'assets/images/hseb_results.png',
                            width: 50,
                            height: 50,
                          )
                          : category.name == 'TU'
                          ? Image.asset(
                            'assets/images/tu_results.png',
                            width: 50,
                            height: 50,
                          )
                          : category.name == 'KU'
                          ? Image.asset(
                            'assets/images/ku_results.png',
                            width: 50,
                            height: 50,
                          )
                          : category.name == 'PU'
                          ? Image.asset(
                            'assets/images/pu_results.png',
                            width: 56,
                            height: 56,
                          )
                          : category.name == 'CTEVT'
                          ? Image.asset(
                            'assets/images/ctevt_results.png',
                            width: 50,
                            height: 50,
                          )
                          : category.name == 'DV'
                          ? Image.asset(
                            'assets/images/dv_lottery_result1.png',
                            width: 52,
                            height: 52,
                          )
                          : category.name == 'IPO'
                          ? Image.asset(
                            'images/cdsc_ipo_results.png',
                            width: 50,
                            height: 50,
                          )
                          : Icon(category.icon, color: categoryColor, size: 50),
                ),

                const SizedBox(height: 16),

                // Category name
                Text(
                  category.name == 'HSEB'
                      ? 'HSEB Results'
                      : category.name == 'SEE'
                      ? 'SEE Results'
                      : category.name == 'TU'
                      ? 'TU Results'
                      : category.name == 'KU'
                      ? 'KU Results'
                      : category.name == 'PU'
                      ? 'PU Results'
                      : category.name == 'CTEVT'
                      ? 'CTEVT Results'
                      : category.name == 'DV'
                      ? 'DV Lottery'
                      : category.name == 'IPO'
                      ? 'IPO Result'
                      : category.name,
                  style: GoogleFonts.poppins(
                    textStyle: TextStyle(
                      fontSize: isDarkMode ? 18 : 16,
                      fontWeight:
                          isDarkMode ? FontWeight.bold : FontWeight.w600,
                      letterSpacing: 0.5,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      shadows:
                          isDarkMode
                              ? [
                                Shadow(
                                  color: categoryColor.withAlpha(150),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ]
                              : null,
                    ),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
